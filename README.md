# DocNet - Smart Document Workflow System

DocNet is a comprehensive document workflow management system designed for businesses to organize, track, and collaborate on document-based projects.

## Features

### 📁 Project Folder Management
- Create, view, edit, and delete project folders
- Track folder metadata: name, creator, creation date, expiry date
- Organize documents and checklists within folders

### 📄 File Upload & Management
- Upload multiple file types (PDF, Word, Excel)
- File metadata tracking and version control
- Secure file storage and retrieval

### ✅ Checklist Management
- Create and manage to-do lists for each folder
- Task assignment and due date tracking
- Progress visualization and completion tracking

### 🔔 Reminder & Notification System
- Automated reminders for due dates and expiry dates
- Email and WhatsApp notifications via Twilio
- 7-day advance notification system

### 🔗 Secure Sharing
- Generate unique, secure shareable links
- View-only and upload-only permissions
- Time-limited access with automatic expiry

### 📱 Mobile-Responsive Frontend
- Clean, WhatsApp-like user interface
- Built with React and Tailwind CSS
- Optimized for mobile and desktop use

## Tech Stack

### Frontend
- React with Vite
- Tailwind CSS for styling
- Responsive design for mobile/desktop

### Backend
- Node.js with Express
- MongoDB for data storage
- File upload handling with multer

### Deployment
- Frontend: Vercel
- Backend: Railway/Render
- Database: MongoDB Atlas

## Project Structure

```
docnet/
├── frontend/          # React application
├── backend/           # Node.js API server
├── shared/            # Shared utilities and types
└── docs/              # Documentation
```

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (local or Atlas)
- npm or yarn

### Installation

1. Clone the repository
2. Install backend dependencies: `cd backend && npm install`
3. Install frontend dependencies: `cd frontend && npm install`
4. Set up environment variables (see .env.example files)
5. Start the development servers

### Development

- Backend: `cd backend && npm run dev`
- Frontend: `cd frontend && npm run dev`

## Environment Variables

See `.env.example` files in both frontend and backend directories for required configuration.

## Contributing

This project is designed to be modular and easily expandable with AI assistance.

## License

MIT License
